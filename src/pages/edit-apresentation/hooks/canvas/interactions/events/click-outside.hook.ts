import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';

interface UseClickOutsideProps {
	propertiesPanelRef: React.RefObject<HTMLDivElement>;
	containerRef: React.RefObject<HTMLDivElement>;
}

export function useClickOutside({ propertiesPanelRef, containerRef }: UseClickOutsideProps) {
	const [, setSelectedIds] = useAtom(selectedItemsIdsAtom);

	const handleClickOutside = useCallback(
		(event: MouseEvent) => {
			const target = event.target as HTMLElement;
			const isInsidePropertiesPanel = propertiesPanelRef.current?.contains(target);
			const isInsideCanvas = containerRef.current?.contains(target);
			const isInsideSelect = Boolean(target.closest('[role="option"], [role="listbox"], .select-content'));
			const isInsideLayerItem = Boolean(target.closest('.elements-layer-item, [data-selection-enabled]'));
			const isInsideNavAction = Boolean(target.closest('[data-nav-action="delete"], [data-nav-action="reset"]'));

			// Verificar se o clique está dentro de componentes de calendário ou popover
			const isInsidePopover = Boolean(target.closest('[data-radix-popover-content], [data-radix-popper-content-wrapper]'));
			const isInsideCalendar = Boolean(target.closest('.rdp, .react-day-picker, [data-radix-calendar]'));
			const isInsidePortal = Boolean(target.closest('[data-radix-portal]'));
			const isInsideTooltip = Boolean(target.closest('[data-radix-tooltip-content]'));
			const isInsideDropdown = Boolean(target.closest('[data-radix-dropdown-menu-content]'));

			if (
				!isInsideCanvas &&
				!isInsidePropertiesPanel &&
				!isInsideSelect &&
				!isInsideLayerItem &&
				!isInsideNavAction &&
				!isInsidePopover &&
				!isInsideCalendar &&
				!isInsidePortal &&
				!isInsideTooltip &&
				!isInsideDropdown
			) {
				setSelectedIds([]);
			}
		},
		[propertiesPanelRef, setSelectedIds, containerRef],
	);

	useEffect(() => {
		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [handleClickOutside]);

	return { containerRef };
}
