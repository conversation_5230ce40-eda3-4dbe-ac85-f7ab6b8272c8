import { motion } from 'framer-motion';
import { useState } from 'react';

import { Button } from '@/components/shadcnui/button';
import { CalendarIcon, Plus } from 'lucide-react';
import { EditorCategoryTitle } from '../../core/components/categorory-title';
import { InputGroup } from '../../core/components/input-group';
import { InputWithLabel, SelectItemProperties } from '../../shared/common';
import { ICalendarObject, MultipleCalendarProps, RangeCalendarProps, SingleCalendarProps } from './calendar.types';
import { useCalendarForm } from './use-calendar-form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/shadcnui/popover';
import { Calendar } from '@/components/shadcnui/calendar';
import { ptBR } from 'date-fns/locale';

interface CalendarEditorProps {
	readonly content: ICalendarObject;
	readonly onChange: (content: ICalendarObject) => void;
}

interface CalendarInputWithLabelProps {
	readonly label: string;
	readonly value: string;
	readonly onChange: (date: Date | undefined) => void;
	readonly readOnly?: boolean;
}

const createLocalDate = (dateString: string): Date => {
	if (dateString.includes('T') || dateString.includes('Z')) {
		return new Date(dateString);
	}
	const [year, month, day] = dateString.split('-').map(Number);
	return new Date(year, month - 1, day);
};

const formatDateToString = (date: Date): string => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
};

const parseDate = (date: Date | string): Date => {
	if (date instanceof Date) {
		return date;
	}
	if (typeof date === 'string') {
		return createLocalDate(date);
	}
	throw new Error('Formato de data inválido');
};

const CalendarInputWithLabel = ({ label, value, onChange, readOnly = false }: CalendarInputWithLabelProps) => {
	const [isOpen, setIsOpen] = useState(false);
	const selectedDate = value ? createLocalDate(value) : undefined;

	const handleDateSelect = (date: Date | undefined) => {
		onChange(date);
		setIsOpen(false);
	};

	const displayValue = selectedDate ? selectedDate.toLocaleDateString('pt-BR') : '';

	return (
		<div className="relative flex flex-col">
			<span className="mb-1 text-xs text-[#6c6c6c]">{label}</span>
			<Popover open={isOpen} onOpenChange={setIsOpen}>
				<PopoverTrigger asChild>
					<div
						className="flex h-7 w-full cursor-pointer items-center rounded bg-[#3a3a3a] px-2"
						onClick={(e) => {
							e.stopPropagation();
							if (!readOnly) {
								setIsOpen(true);
							}
						}}
					>
						<input
							type="text"
							value={displayValue}
							readOnly
							className="h-full w-full cursor-pointer bg-transparent text-xs text-[#f0f0f0] outline-none"
							placeholder="Selecione uma data"
						/>
						<CalendarIcon className="h-4 w-4 text-[#b0b0b0]" />
					</div>
				</PopoverTrigger>
				<PopoverContent className="w-auto p-0" align="start" onPointerDown={(e) => e.stopPropagation()}>
					<Calendar
						mode="single"
						locale={ptBR}
						selected={selectedDate}
						onSelect={(date) => {
							handleDateSelect(date);
						}}
						disabled={(date) => date < new Date('1900-01-01')}
						initialFocus
					/>
				</PopoverContent>
			</Popover>
		</div>
	);
};

const CalendarLayoutSettings = ({
	calendarState,
	updateCalendarField,
}: {
	calendarState: ICalendarObject;
	updateCalendarField: (field: keyof ICalendarObject, value: ICalendarObject[typeof field]) => void;
}) => {
	if (calendarState.calendarMode === 'single') return null;

	return (
		<InputGroup ariaLabel="Layout do calendário">
			<SelectItemProperties
				value={String(calendarState.numberOfMonths ?? 3)}
				onValueChange={(value) => updateCalendarField('numberOfMonths', Number(value))}
				items={[
					{ label: '1 mês', value: '1' },
					{ label: '2 meses', value: '2' },
					{ label: '3 meses', value: '3' },
					{ label: '4 meses', value: '4' },
				]}
				labelText="Número de meses:"
			/>
		</InputGroup>
	);
};

const ColorSettings = ({
	calendarState,
	updateCalendarField,
}: {
	calendarState: ICalendarObject;
	updateCalendarField: (field: keyof ICalendarObject, value: ICalendarObject[typeof field]) => void;
}) => (
	<div className="flex flex-col gap-2">
		<InputGroup columns={2} ariaLabel="Propriedades do calendário">
			<InputWithLabel
				label="Opacidade"
				type="number"
				value={calendarState.opacity}
				onChange={(e) => updateCalendarField('opacity', Number(e.target.value))}
				sizeUnit="%"
			/>
			<InputWithLabel
				label="Cor de fundo"
				type="color"
				value={calendarState.backgroundColor}
				onChange={(e) => updateCalendarField('backgroundColor', e.target.value)}
			/>
		</InputGroup>
		<InputGroup columns={2} ariaLabel="Cores do calendário">
			<InputWithLabel
				label="Cor principal"
				type="color"
				value={calendarState.mainColor}
				onChange={(e) => updateCalendarField('mainColor', e.target.value)}
			/>
			<InputWithLabel
				label="Cor secundária"
				type="color"
				value={calendarState.secondaryColor}
				onChange={(e) => updateCalendarField('secondaryColor', e.target.value)}
			/>
		</InputGroup>
		<InputGroup ariaLabel="Cores do calendário">
			<InputWithLabel
				label="Cor de texto"
				type="color"
				value={calendarState.textColor}
				onChange={(e) => updateCalendarField('textColor', e.target.value)}
			/>
		</InputGroup>
	</div>
);

const StyleSettings = ({
	calendarState,
	updateCalendarField,
}: {
	calendarState: ICalendarObject;
	updateCalendarField: (field: keyof ICalendarObject, value: ICalendarObject[typeof field]) => void;
}) => (
	<div className="flex flex-col gap-2">
		<EditorCategoryTitle title="Espaçamento" className="mt-4 text-xs uppercase" />
		<InputGroup columns={2} ariaLabel="Padding">
			<InputWithLabel
				label="Padding Superior"
				type="number"
				value={calendarState.padding?.top ?? 0}
				onChange={(e) => updateCalendarField('padding', { ...calendarState.padding, top: Number(e.target.value) })}
				sizeUnit="px"
			/>
			<InputWithLabel
				label="Padding Direito"
				type="number"
				value={calendarState.padding?.right ?? 0}
				onChange={(e) => updateCalendarField('padding', { ...calendarState.padding, right: Number(e.target.value) })}
				sizeUnit="px"
			/>
			<InputWithLabel
				label="Padding Inferior"
				type="number"
				value={calendarState.padding?.bottom ?? 0}
				onChange={(e) => updateCalendarField('padding', { ...calendarState.padding, bottom: Number(e.target.value) })}
				sizeUnit="px"
			/>
			<InputWithLabel
				label="Padding Esquerdo"
				type="number"
				value={calendarState.padding?.left ?? 0}
				onChange={(e) => updateCalendarField('padding', { ...calendarState.padding, left: Number(e.target.value) })}
				sizeUnit="px"
			/>
		</InputGroup>

		<InputGroup ariaLabel="Espaçamento entre células">
			<InputWithLabel
				label="Espaçamento entre células"
				type="number"
				value={calendarState.cellSpacing}
				onChange={(e) => updateCalendarField('cellSpacing', Number(e.target.value))}
				sizeUnit="px"
			/>
		</InputGroup>

		<EditorCategoryTitle title="Borda" className="mt-4 text-xs uppercase" />
		<InputGroup columns={2} ariaLabel="Configurações de borda">
			<InputWithLabel
				label="Largura da borda"
				type="number"
				value={calendarState.border?.width}
				onChange={(e) => updateCalendarField('border', { ...calendarState.border, width: Number(e.target.value) })}
				sizeUnit="px"
			/>
			<InputWithLabel
				label="Cor da borda"
				type="color"
				value={calendarState.border?.color}
				onChange={(e) => updateCalendarField('border', { ...calendarState.border, color: e.target.value })}
			/>
			<SelectItemProperties
				value={calendarState.border?.style}
				onValueChange={(value) => updateCalendarField('border', { ...calendarState.border, style: value })}
				items={[
					{ label: 'Nenhuma', value: 'none' },
					{ label: 'Sólida', value: 'solid' },
					{ label: 'Tracejada', value: 'dashed' },
					{ label: 'Pontilhada', value: 'dotted' },
				]}
				labelText="Estilo da borda:"
			/>
			<InputWithLabel
				label="Raio da borda"
				type="number"
				value={calendarState.border?.radius}
				onChange={(e) => updateCalendarField('border', { ...calendarState.border, radius: Number(e.target.value) })}
				sizeUnit="px"
			/>
		</InputGroup>
	</div>
);

const RangeCalendarInputs = ({
	calendarState,
	handleRangeCalendarUpdate,
}: {
	calendarState: RangeCalendarProps;
	handleRangeCalendarUpdate: (field: keyof RangeCalendarProps, value: RangeCalendarProps[typeof field]) => void;
}) => {
	const fromDate = calendarState.dateRange?.from ? calendarState.dateRange.from : null;
	const toDate = calendarState.dateRange?.to ? calendarState.dateRange.to : null;

	const handleFromDateChange = (date: Date) => {
		handleRangeCalendarUpdate('dateRange', {
			from: date,
			to: toDate || date,
		});
	};

	const handleToDateChange = (date: Date) => {
		if (fromDate && date < fromDate) return;

		handleRangeCalendarUpdate('dateRange', {
			from: fromDate || date,
			to: date,
		});
	};

	return (
		<InputGroup columns={2} ariaLabel="Opções de período">
			<CalendarInputWithLabel
				label="Data inicial"
				value={fromDate ? formatDateToString(fromDate) : ''}
				onChange={(date) => {
					if (date) {
						handleFromDateChange(date);
					}
				}}
			/>
			<CalendarInputWithLabel
				label="Data final"
				value={toDate ? formatDateToString(toDate) : ''}
				onChange={(date) => {
					if (date) {
						handleToDateChange(date);
					}
				}}
				readOnly={!fromDate}
			/>
		</InputGroup>
	);
};

const MultipleCalendarInputs = ({
	calendarState,
	handleMultipleCalendarUpdate,
}: {
	calendarState: MultipleCalendarProps;
	handleMultipleCalendarUpdate: (field: keyof MultipleCalendarProps, value: MultipleCalendarProps[typeof field]) => void;
}) => {
	const [newDate, setNewDate] = useState<string>('');
	const [error, setError] = useState<string | null>(null);

	const isValidDate = (dateStr: string): boolean => {
		if (!dateStr || typeof dateStr !== 'string') return false;
		try {
			const date = parseDate(dateStr);
			return !isNaN(date.getTime());
		} catch {
			return false;
		}
	};

	const normalizeDate = (date: Date | string): string => {
		try {
			const parsedDate = parseDate(date);
			return formatDateToString(parsedDate);
		} catch {
			return typeof date === 'string' ? date : '';
		}
	};

	const handleAddDate = () => {
		setError(null);

		if (!newDate) {
			setError('Selecione uma data');
			return;
		}

		if (!isValidDate(newDate)) {
			setError('Data inválida');
			return;
		}

		try {
			const dateToAdd = parseDate(newDate);
			const currentDates = calendarState.selectedDates || [];
			const normalizedNewDate = normalizeDate(dateToAdd);

			if (currentDates.some((date) => normalizeDate(date) === normalizedNewDate)) {
				setError('Esta data já foi adicionada');
				return;
			}

			// Sempre salvar como objeto Date
			handleMultipleCalendarUpdate('selectedDates', [...currentDates, dateToAdd]);
			setNewDate('');
		} catch (error) {
			setError('Erro ao processar a data');
		}
	};

	const handleRemoveDate = (dateToRemove: Date | string) => {
		const currentDates = calendarState.selectedDates || [];
		const normalizedDateToRemove = normalizeDate(dateToRemove);

		handleMultipleCalendarUpdate(
			'selectedDates',
			currentDates.filter((date) => normalizeDate(date) !== normalizedDateToRemove),
		);
	};

	return (
		<InputGroup ariaLabel="Opções de múltiplas datas">
			<div className="flex flex-col gap-3">
				<div className="flex flex-col gap-2">
					<div className="flex items-end gap-2">
						<div className="flex-1">
							<CalendarInputWithLabel
								label="Adicionar nova data"
								value={newDate}
								onChange={(date) => {
									if (date) {
										setNewDate(formatDateToString(date));
										if (error) setError(null);
									}
								}}
							/>
							{error && (
								<motion.p initial={{ opacity: 0, y: -5 }} animate={{ opacity: 1, y: 0 }} className="mt-1 text-xs text-red-500">
									{error}
								</motion.p>
							)}
						</div>
						<motion.div
							initial={{ scale: 0, opacity: 0 }}
							animate={{ scale: newDate ? 1 : 0, opacity: newDate ? 1 : 0 }}
							exit={{ scale: 0, opacity: 0 }}
							transition={{ duration: 0.2 }}
							className={`flex-shrink-0 ${error ? 'mb-6' : ''}`}
						>
							<Button
								type="button"
								size="icon"
								variant="outline"
								className="flex h-7 w-7 items-center justify-center border-[#555] bg-[#3a3a3a] p-0 hover:bg-[#4a4a4a]"
								onClick={(e) => {
									e.stopPropagation();
									handleAddDate();
								}}
								disabled={!newDate}
							>
								<Plus className="h-4 w-4" />
							</Button>
						</motion.div>
					</div>
				</div>

				<div className="flex flex-col gap-1">
					<EditorCategoryTitle title="Datas selecionadas" className="text-xs" />
					<motion.div className="grid grid-cols-2 gap-1" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
						{calendarState.selectedDates?.map((date, index) => (
							<motion.div
								key={normalizeDate(date) + '-' + index}
								initial={{ opacity: 0, x: -20 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: 20 }}
								transition={{ duration: 0.2, delay: index * 0.05 }}
								className="flex min-w-0 max-w-full items-center justify-between gap-1 overflow-hidden rounded border bg-gray-100/10 px-2 py-1 text-[11px] shadow-sm"
							>
								<span className="truncate text-gray-200">
									{(() => {
										try {
											// Usar parseDate para lidar com diferentes formatos
											const parsedDate = parseDate(date);
											return parsedDate.toLocaleDateString('pt-BR');
										} catch (error) {
											console.error('Erro ao processar data:', date, error);
											return 'Erro na data';
										}
									})()}
								</span>
								<motion.button
									type="button"
									onClick={() => handleRemoveDate(date)}
									className="ml-1 flex h-4 w-4 items-center justify-center rounded-full text-xs font-bold text-gray-400 transition-colors hover:bg-gray-700 hover:text-red-400"
									title="Remover data"
									whileHover={{ scale: 1.1 }}
									whileTap={{ scale: 0.9 }}
								>
									x
								</motion.button>
							</motion.div>
						))}
						{(!calendarState.selectedDates || calendarState.selectedDates.length === 0) && (
							<motion.div
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ duration: 0.3 }}
								className="col-span-2 text-[11px] italic text-gray-400"
							>
								Nenhuma data selecionada
							</motion.div>
						)}
					</motion.div>
				</div>
			</div>
		</InputGroup>
	);
};

export function CalendarEditor({ content, onChange }: CalendarEditorProps) {
	const { formData: calendarState, updateField: updateCalendarField } = useCalendarForm(content, onChange);

	console.log('calendarState', calendarState);

	const handleSingleCalendarUpdate = (field: keyof SingleCalendarProps, value: SingleCalendarProps[typeof field]) => {
		if (calendarState.calendarMode === 'single') {
			updateCalendarField(field as keyof ICalendarObject, value);
		}
	};

	const handleMultipleCalendarUpdate = (field: keyof MultipleCalendarProps, value: MultipleCalendarProps[typeof field]) => {
		if (calendarState.calendarMode === 'multiple') {
			updateCalendarField(field as keyof ICalendarObject, value);
		}
	};

	const handleRangeCalendarUpdate = (field: keyof RangeCalendarProps, value: RangeCalendarProps[typeof field]) => {
		if (calendarState.calendarMode === 'range') {
			updateCalendarField(field as keyof ICalendarObject, value);
		}
	};

	return (
		<section aria-labelledby="calendar-editor" className="flex flex-col gap-2">
			<EditorCategoryTitle title="Calendário" className="text-xs uppercase" />
			<form
				className="w-full flex-col"
				onSubmit={(e) => {
					e.preventDefault();
				}}
			>
				<ColorSettings calendarState={calendarState} updateCalendarField={updateCalendarField} />
				<InputGroup ariaLabel="Propriedades do calendário 2">
					<SelectItemProperties
						value={calendarState.calendarMode}
						onValueChange={(value) => updateCalendarField('calendarMode', value as 'single' | 'multiple' | 'range')}
						items={[
							{ label: 'Mês único', value: 'single' },
							{ label: 'Múltiplos dias', value: 'multiple' },
							{ label: 'Período de tempo', value: 'range' },
						]}
						labelText="Modo do calendário:"
					/>
				</InputGroup>
				<CalendarLayoutSettings calendarState={calendarState} updateCalendarField={updateCalendarField} />
				<StyleSettings calendarState={calendarState} updateCalendarField={updateCalendarField} />
				{calendarState.calendarMode === 'single' && (
					<InputGroup ariaLabel="Opções de data única">
						<CalendarInputWithLabel
							label="Data selecionada"
							value={calendarState.selectedDate ? formatDateToString(calendarState.selectedDate) : ''}
							onChange={(date) => {
								if (date) {
									handleSingleCalendarUpdate('selectedDate', date);
								}
							}}
						/>
						<SelectItemProperties
							value={String(Boolean(calendarState.useToday))}
							onValueChange={(value) => handleSingleCalendarUpdate('useToday', value === 'true')}
							items={[
								{ label: 'Sim', value: 'true' },
								{ label: 'Não', value: 'false' },
							]}
							labelText="Usar data atual:"
						/>
					</InputGroup>
				)}
				{calendarState.calendarMode === 'multiple' && (
					<MultipleCalendarInputs calendarState={calendarState} handleMultipleCalendarUpdate={handleMultipleCalendarUpdate} />
				)}
				{calendarState.calendarMode === 'range' && (
					<RangeCalendarInputs calendarState={calendarState} handleRangeCalendarUpdate={handleRangeCalendarUpdate} />
				)}
				<InputGroup ariaLabel="Opções adicionais">
					<SelectItemProperties
						value={String(calendarState.showOutsideDays)}
						onValueChange={(value) => updateCalendarField('showOutsideDays', value === 'true')}
						items={[
							{ label: 'Sim', value: 'true' },
							{ label: 'Não', value: 'false' },
						]}
						labelText="Mostrar dias fora do mês:"
					/>
				</InputGroup>
			</form>
		</section>
	);
}
