// Teste rápido para validar as correções de data
const testData = {
    "border": {
        "color": "#000000",
        "style": "none",
        "width": 0,
        "radius": 0
    },
    "opacity": 1,
    "padding": {
        "top": 8,
        "left": 8,
        "right": 8,
        "bottom": 8
    },
    "mainColor": "#000000",
    "textColor": "#000000",
    "cellSpacing": 0,
    "calendarMode": "multiple",
    "selectedDates": [
        "2025-06-25T03:00:00.000Z"
    ],
    "secondaryColor": "#371616",
    "backgroundColor": "transparent",
    "showOutsideDays": true
};

// Função de teste para simular a parseDate
const testParseDate = (date) => {
    if (date instanceof Date) {
        return date;
    }
    if (typeof date === 'string') {
        // Se a string contém 'T' ou 'Z', é uma string ISO
        if (date.includes('T') || date.includes('Z')) {
            return new Date(date);
        }
        
        // <PERSON>aso contr<PERSON>rio, assumir formato "YYYY-MM-DD"
        const [year, month, day] = date.split('-').map(Number);
        return new Date(year, month - 1, day);
    }
    throw new Error('Formato de data inválido');
};

// Teste
console.log('Data original:', testData.selectedDates[0]);
const parsedDate = testParseDate(testData.selectedDates[0]);
console.log('Data parseada:', parsedDate);
console.log('Data formatada para pt-BR:', parsedDate.toLocaleDateString('pt-BR'));
console.log('É válida?', !isNaN(parsedDate.getTime()));
